<?php
/*
 * مدير انتهاء اشتراكات PPP - PPP Expiry Manager
 * ملف اختياري لإدارة انتهاء الاشتراكات باستخدام قاعدة البيانات
 */

session_start();
error_reporting(0);

if (!isset($_SESSION["mikhmon"])) {
    header("Location:../admin.php?id=login");
    exit;
}

// إعدادات قاعدة البيانات (يجب تعديلها حسب الإعداد)
$db_host = 'localhost';
$db_name = 'mikhmon';
$db_user = 'root';
$db_pass = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دالة لإضافة/تحديث بيانات انتهاء المشترك
function updateUserExpiry($pdo, $username, $profile, $expiry_date, $duration_days) {
    $sql = "INSERT INTO ppp_expiry_tracking (username, profile_name, start_date, expiry_date, duration_days) 
            VALUES (?, ?, CURDATE(), ?, ?) 
            ON DUPLICATE KEY UPDATE 
            profile_name = VALUES(profile_name),
            expiry_date = VALUES(expiry_date),
            duration_days = VALUES(duration_days),
            updated_at = CURRENT_TIMESTAMP";
    
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$username, $profile, $expiry_date, $duration_days]);
}

// دالة لجلب المشتركين القريبين من الانتهاء
function getExpiringUsers($pdo, $days = 7) {
    $sql = "SELECT * FROM ppp_expiring_soon WHERE days_remaining <= ? AND days_remaining >= 0 ORDER BY days_remaining ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$days]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// دالة لجلب المشتركين المنتهيين
function getExpiredUsers($pdo) {
    $sql = "SELECT * FROM ppp_expiring_soon WHERE days_remaining < 0 ORDER BY days_remaining ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// دالة لجلب إحصائيات الانتهاء
function getExpiryStats($pdo) {
    $sql = "SELECT 
                alert_level,
                COUNT(*) as count
            FROM ppp_expiring_soon 
            GROUP BY alert_level";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['sync_from_mikrotik'])) {
        // مزامنة البيانات من MikroTik
        $secrets = $API->comm("/ppp/secret/print");
        $synced = 0;
        
        foreach ($secrets as $secret) {
            if (isset($secret['comment']) && preg_match('/expiry_date=([0-9\-]+)/', $secret['comment'], $matches)) {
                $expiry_date = $matches[1];
                $profile = $secret['profile'];
                
                // استخراج مدة الباقة من البروفايل
                $profile_data = $API->comm("/ppp/profile/print", array("?name" => $profile));
                $duration_days = 30; // افتراضي
                if (isset($profile_data[0]['comment']) && preg_match('/duration_days=(\d+)/', $profile_data[0]['comment'], $matches2)) {
                    $duration_days = intval($matches2[1]);
                }
                
                if (updateUserExpiry($pdo, $secret['name'], $profile, $expiry_date, $duration_days)) {
                    $synced++;
                }
            }
        }
        
        $message = "تم مزامنة $synced مشترك من MikroTik";
    }
}

// جلب البيانات للعرض
$expiring_users = getExpiringUsers($pdo, 7);
$expired_users = getExpiredUsers($pdo);
$stats = getExpiryStats($pdo);
?>

<!DOCTYPE html>
<html>
<head>
    <title>مدير انتهاء الاشتراكات</title>
    <meta charset="utf-8">
    <style>
        .expired { color: #dc3545; }
        .warning { color: #ffc107; }
        .normal { color: #28a745; }
        .stats-card { 
            display: inline-block; 
            margin: 10px; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>مدير انتهاء اشتراكات PPP</h2>
        
        <?php if (isset($message)): ?>
            <div class="alert alert-success"><?= $message ?></div>
        <?php endif; ?>
        
        <!-- إحصائيات -->
        <div class="stats-section">
            <h3>الإحصائيات</h3>
            <?php foreach ($stats as $stat): ?>
                <div class="stats-card <?= $stat['alert_level'] ?>">
                    <h4><?= $stat['count'] ?></h4>
                    <p><?= $stat['alert_level'] == 'expired' ? 'منتهي' : ($stat['alert_level'] == 'warning' ? 'تحذير' : 'عادي') ?></p>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- أزرار الإدارة -->
        <div class="management-section">
            <form method="post" style="display: inline;">
                <button type="submit" name="sync_from_mikrotik" class="btn btn-primary">
                    مزامنة من MikroTik
                </button>
            </form>
        </div>
        
        <!-- المشتركين القريبين من الانتهاء -->
        <div class="expiring-section">
            <h3>المشتركين القريبين من الانتهاء (خلال 7 أيام)</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>البروفايل</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الأيام المتبقية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expiring_users as $user): ?>
                        <tr>
                            <td><?= htmlspecialchars($user['username']) ?></td>
                            <td><?= htmlspecialchars($user['profile_name']) ?></td>
                            <td><?= $user['expiry_date'] ?></td>
                            <td class="<?= $user['alert_level'] ?>"><?= $user['days_remaining'] ?></td>
                            <td class="<?= $user['alert_level'] ?>">
                                <?= $user['alert_level'] == 'warning' ? 'تحذير' : 'عادي' ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- المشتركين المنتهيين -->
        <div class="expired-section">
            <h3>المشتركين المنتهيين</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>البروفايل</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الأيام المنقضية</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expired_users as $user): ?>
                        <tr class="expired">
                            <td><?= htmlspecialchars($user['username']) ?></td>
                            <td><?= htmlspecialchars($user['profile_name']) ?></td>
                            <td><?= $user['expiry_date'] ?></td>
                            <td><?= abs($user['days_remaining']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
