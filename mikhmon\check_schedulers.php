<?php
/*
 * فحص جميع الـ schedulers في MikroTik للتأكد من وجود scheduler التعطيل
 */

session_start();
error_reporting(0);

if (!isset($_SESSION["mikhmon"])) {
    header("Location:../admin.php?id=login");
    exit;
}

include('../lib/routeros_api.class.php');
include('../config/config.php');

$API = new RouterosAPI();
$API->debug = false;

if ($API->connect($mikrotik_ip, $mikrotik_username, $mikrotik_password, $mikrotik_port)) {
    echo "<h2>فحص جميع الـ Schedulers في MikroTik</h2>";
    
    // جلب جميع الـ schedulers
    $schedulers = $API->comm("/system/scheduler/print");
    
    echo "<h3>إجمالي عدد الـ Schedulers: " . count($schedulers) . "</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th>الاسم</th>";
    echo "<th>تاريخ البداية</th>";
    echo "<th>وقت البداية</th>";
    echo "<th>الفترة</th>";
    echo "<th>التشغيل التالي</th>";
    echo "<th>النوع</th>";
    echo "<th>التعليق</th>";
    echo "</tr>";
    
    $monitor_count = 0;
    $disable_count = 0;
    $other_count = 0;
    
    foreach ($schedulers as $scheduler) {
        $name = isset($scheduler['name']) ? $scheduler['name'] : '';
        $start_date = isset($scheduler['start-date']) ? $scheduler['start-date'] : '';
        $start_time = isset($scheduler['start-time']) ? $scheduler['start-time'] : '';
        $interval = isset($scheduler['interval']) ? $scheduler['interval'] : '';
        $next_run = isset($scheduler['next-run']) ? $scheduler['next-run'] : '';
        $comment = isset($scheduler['comment']) ? $scheduler['comment'] : '';
        
        // تحديد نوع الـ scheduler
        $type = '';
        $type_color = '';
        if (strpos($name, 'monitor-') === 0) {
            $type = 'مراقبة';
            $type_color = '#007bff';
            $monitor_count++;
        } elseif (strpos($name, 'disable-ppp-') === 0) {
            $type = 'تعطيل تلقائي';
            $type_color = '#dc3545';
            $disable_count++;
        } else {
            $type = 'أخرى';
            $type_color = '#6c757d';
            $other_count++;
        }
        
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($name) . "</strong></td>";
        echo "<td>" . htmlspecialchars($start_date) . "</td>";
        echo "<td>" . htmlspecialchars($start_time) . "</td>";
        echo "<td>" . htmlspecialchars($interval) . "</td>";
        echo "<td>" . htmlspecialchars($next_run) . "</td>";
        echo "<td style='color: $type_color; font-weight: bold;'>$type</td>";
        echo "<td>" . htmlspecialchars($comment) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>إحصائيات الـ Schedulers:</h3>";
    echo "<ul>";
    echo "<li><strong style='color: #007bff;'>المراقبة:</strong> $monitor_count</li>";
    echo "<li><strong style='color: #dc3545;'>التعطيل التلقائي:</strong> $disable_count</li>";
    echo "<li><strong style='color: #6c757d;'>أخرى:</strong> $other_count</li>";
    echo "</ul>";
    
    // البحث عن المستخدم 99 تحديداً
    echo "<h3>البحث عن schedulers المستخدم '99':</h3>";
    $user_schedulers = [];
    foreach ($schedulers as $scheduler) {
        $name = isset($scheduler['name']) ? $scheduler['name'] : '';
        if (strpos($name, '-99') !== false || strpos($name, '99') !== false) {
            $user_schedulers[] = $scheduler;
        }
    }
    
    if (count($user_schedulers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #fff3cd;'>";
        echo "<th>الاسم</th>";
        echo "<th>تاريخ البداية</th>";
        echo "<th>وقت البداية</th>";
        echo "<th>التشغيل التالي</th>";
        echo "<th>الحالة</th>";
        echo "</tr>";
        
        foreach ($user_schedulers as $scheduler) {
            $name = isset($scheduler['name']) ? $scheduler['name'] : '';
            $start_date = isset($scheduler['start-date']) ? $scheduler['start-date'] : '';
            $start_time = isset($scheduler['start-time']) ? $scheduler['start-time'] : '';
            $next_run = isset($scheduler['next-run']) ? $scheduler['next-run'] : '';
            
            $status = '';
            $status_color = '';
            if (strpos($name, 'monitor-') === 0) {
                $status = 'مراقبة - يعمل حسب الفترة المحددة';
                $status_color = '#007bff';
            } elseif (strpos($name, 'disable-ppp-') === 0) {
                $status = 'تعطيل تلقائي - سيعمل مرة واحدة في تاريخ الانتهاء';
                $status_color = '#dc3545';
            }
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($name) . "</strong></td>";
            echo "<td>" . htmlspecialchars($start_date) . "</td>";
            echo "<td>" . htmlspecialchars($start_time) . "</td>";
            echo "<td>" . htmlspecialchars($next_run) . "</td>";
            echo "<td style='color: $status_color;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: #dc3545;'>لم يتم العثور على schedulers للمستخدم '99'</p>";
    }
    
    // توضيح الفرق
    echo "<div style='background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #0c5460;'>📋 توضيح أنواع الـ Schedulers:</h4>";
    echo "<ul style='color: #0c5460;'>";
    echo "<li><strong>monitor-username:</strong> للمراقبة - يعمل حسب الفترة المحددة (مثل كل 30 يوم)</li>";
    echo "<li><strong>disable-ppp-username:</strong> للتعطيل التلقائي - يعمل مرة واحدة في تاريخ انتهاء الاشتراك</li>";
    echo "</ul>";
    echo "<p style='color: #0c5460;'><strong>الـ scheduler في الصورة (monitor-99) طبيعي</strong> - هو للمراقبة وليس للتعطيل التلقائي.</p>";
    echo "</div>";
    
    $API->disconnect();
} else {
    echo "<p style='color: #dc3545;'>خطأ في الاتصال بـ MikroTik</p>";
}
?>
