<?php
/*
 * ملف اختبار لفحص إصلاحات نظام الجدولة
 * Test file for scheduler system fixes
 */

// محاكاة بيانات للاختبار
echo "<h2>اختبار إصلاحات نظام الجدولة - Scheduler System Fixes Test</h2>";

// اختبار حساب الأيام المتبقية
function test_days_calculation() {
    echo "<h3>اختبار حساب الأيام المتبقية:</h3>";
    
    // تاريخ انتهاء في المستقبل
    $future_date = date('Y-m-d', strtotime('+10 days'));
    $now = new DateTime();
    $exp = DateTime::createFromFormat('Y-m-d', $future_date);
    if ($exp) {
        $exp->setTime(23, 59, 59);
        $interval = $now->diff($exp);
        $days_left = $interval->format('%r%a');
        echo "تاريخ الانتهاء: $future_date - الأيام المتبقية: $days_left<br>";
    }
    
    // تاريخ انتهاء في الماضي
    $past_date = date('Y-m-d', strtotime('-5 days'));
    $exp = DateTime::createFromFormat('Y-m-d', $past_date);
    if ($exp) {
        $exp->setTime(23, 59, 59);
        $interval = $now->diff($exp);
        $days_left = $interval->format('%r%a');
        echo "تاريخ الانتهاء: $past_date - الأيام المتبقية: $days_left (منتهي)<br>";
    }
    
    // تاريخ اليوم
    $today = date('Y-m-d');
    $exp = DateTime::createFromFormat('Y-m-d', $today);
    if ($exp) {
        $exp->setTime(23, 59, 59);
        $interval = $now->diff($exp);
        $days_left = $interval->format('%r%a');
        echo "تاريخ الانتهاء: $today - الأيام المتبقية: $days_left<br>";
    }
}

// اختبار تحويل التاريخ
function test_date_conversion() {
    echo "<h3>اختبار تحويل التاريخ:</h3>";
    
    $mikrotik_date = date('M/d/Y', strtotime('+15 days'));
    echo "تاريخ MikroTik: $mikrotik_date<br>";
    
    $standard_date = date('Y-m-d', strtotime('+15 days'));
    echo "التاريخ القياسي: $standard_date<br>";
    
    // تحويل من MikroTik إلى قياسي
    $converted = DateTime::createFromFormat('M/d/Y', $mikrotik_date);
    if ($converted) {
        echo "التحويل من MikroTik: " . $converted->format('Y-m-d') . "<br>";
    }
}

// اختبار استخراج مدة الباقة من التعليق
function test_duration_extraction() {
    echo "<h3>اختبار استخراج مدة الباقة:</h3>";
    
    $comment_examples = [
        "duration_days=30",
        "duration_days=7",
        "duration_days=365",
        "other_info duration_days=15 more_info",
        "no_duration_here"
    ];
    
    foreach ($comment_examples as $comment) {
        $duration_days = 0;
        if (preg_match('/duration_days=(\d+)/', $comment, $matches)) {
            $duration_days = intval($matches[1]);
        }
        echo "التعليق: '$comment' - المدة: $duration_days أيام<br>";
    }
}

// اختبار استخراج تاريخ الانتهاء من التعليق
function test_expiry_extraction() {
    echo "<h3>اختبار استخراج تاريخ الانتهاء:</h3>";
    
    $comment_examples = [
        "expiry_date=2024-12-31",
        "expiry_date=2024-01-15",
        "other_info expiry_date=2024-06-30 more_info",
        "no_expiry_here"
    ];
    
    foreach ($comment_examples as $comment) {
        $expiry_date = '';
        if (preg_match('/expiry_date=([0-9\-]+)/', $comment, $matches)) {
            $expiry_date = $matches[1];
        }
        echo "التعليق: '$comment' - تاريخ الانتهاء: " . ($expiry_date ? $expiry_date : 'غير محدد') . "<br>";
    }
}

// تشغيل الاختبارات
test_days_calculation();
echo "<hr>";
test_date_conversion();
echo "<hr>";
test_duration_extraction();
echo "<hr>";
test_expiry_extraction();

echo "<h3>ملخص الإصلاحات المطبقة:</h3>";
echo "<ul>";
echo "<li>إصلاح مشكلة إنشاء scheduler مكرر في addsecret.php</li>";
echo "<li>تحسين حساب الأيام المتبقية مع إضافة ألوان تحذيرية</li>";
echo "<li>إصلاح عرض تاريخ الانتهاء في قائمة المشتركين</li>";
echo "<li>تحسين البحث عن scheduler بالاسم الصحيح</li>";
echo "<li>إضافة دعم للتواريخ من كل من Scheduler والتعليقات</li>";
echo "</ul>";

echo "<h3>التحسينات المضافة:</h3>";
echo "<ul>";
echo "<li>إضافة ألوان للأيام المتبقية (أحمر للمنتهي، أصفر للقريب، أخضر للعادي)</li>";
echo "<li>تحسين دقة حساب الوقت بتعيين نهاية اليوم</li>";
echo "<li>فصل scheduler المراقبة عن scheduler التعطيل</li>";
echo "<li>تحسين معالجة الأخطاء في تحويل التواريخ</li>";
echo "</ul>";
?>
