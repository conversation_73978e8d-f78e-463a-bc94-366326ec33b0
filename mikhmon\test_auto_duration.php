<?php
/*
 * اختبار النظام الجديد لأخذ مدة الاشتراك تلقائياً من البروفايل
 * Test file for automatic subscription duration from profile
 */

echo "<h2>اختبار النظام الجديد - Auto Duration System Test</h2>";

// محاكاة بيانات البروفايلات
$sample_profiles = [
    [
        'name' => 'profile-30days',
        'comment' => 'duration_days=30',
        'rate-limit' => '10M/10M'
    ],
    [
        'name' => 'profile-7days',
        'comment' => 'duration_days=7',
        'rate-limit' => '5M/5M'
    ],
    [
        'name' => 'profile-unlimited',
        'comment' => 'unlimited profile',
        'rate-limit' => '20M/20M'
    ],
    [
        'name' => 'profile-365days',
        'comment' => 'duration_days=365 yearly subscription',
        'rate-limit' => '50M/50M'
    ]
];

echo "<h3>اختبار استخراج مدة الباقة من البروفايلات:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f8f9fa;'>";
echo "<th>اسم البروفايل</th>";
echo "<th>التعليق</th>";
echo "<th>المدة المستخرجة</th>";
echo "<th>الحالة</th>";
echo "</tr>";

foreach ($sample_profiles as $profile) {
    $duration_days = 0;
    if (preg_match('/duration_days=(\d+)/', $profile['comment'], $matches)) {
        $duration_days = intval($matches[1]);
    }
    
    $status = $duration_days > 0 ? "✅ محدد" : "❌ غير محدد";
    $status_color = $duration_days > 0 ? "#28a745" : "#dc3545";
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($profile['name']) . "</td>";
    echo "<td>" . htmlspecialchars($profile['comment']) . "</td>";
    echo "<td style='text-align: center; font-weight: bold;'>" . ($duration_days > 0 ? $duration_days . " أيام" : "غير محدد") . "</td>";
    echo "<td style='color: $status_color; text-align: center;'>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>اختبار حساب تاريخ الانتهاء:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f8f9fa;'>";
echo "<th>البروفايل</th>";
echo "<th>المدة (أيام)</th>";
echo "<th>تاريخ البداية</th>";
echo "<th>تاريخ الانتهاء</th>";
echo "<th>تاريخ الانتهاء (MikroTik)</th>";
echo "</tr>";

foreach ($sample_profiles as $profile) {
    $duration_days = 0;
    if (preg_match('/duration_days=(\d+)/', $profile['comment'], $matches)) {
        $duration_days = intval($matches[1]);
    }
    
    if ($duration_days > 0) {
        $start_date = date('Y-m-d');
        $expiry_date = date('Y-m-d', strtotime("+{$duration_days} days"));
        $expiry_date_mikrotik = date('M/d/Y', strtotime("+{$duration_days} days"));
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($profile['name']) . "</td>";
        echo "<td style='text-align: center;'>$duration_days</td>";
        echo "<td style='text-align: center;'>$start_date</td>";
        echo "<td style='text-align: center; color: #28a745;'>$expiry_date</td>";
        echo "<td style='text-align: center; color: #007bff;'>$expiry_date_mikrotik</td>";
        echo "</tr>";
    }
}

echo "</table>";

echo "<h3>محاكاة إنشاء Scheduler:</h3>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

$test_username = "test-user-123";
$test_profile = "profile-30days";
$duration_days = 30;
$expiry_date_mikrotik = date('M/d/Y', strtotime("+{$duration_days} days"));

echo "<h4>بيانات المستخدم الاختباري:</h4>";
echo "<ul>";
echo "<li><strong>اسم المستخدم:</strong> $test_username</li>";
echo "<li><strong>البروفايل:</strong> $test_profile</li>";
echo "<li><strong>مدة الاشتراك:</strong> $duration_days أيام</li>";
echo "<li><strong>تاريخ الانتهاء:</strong> $expiry_date_mikrotik</li>";
echo "</ul>";

echo "<h4>Scheduler التعطيل التلقائي:</h4>";
echo "<pre style='background-color: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "Name: disable-ppp-$test_username\n";
echo "Start Date: $expiry_date_mikrotik\n";
echo "Start Time: 00:00:01\n";
echo "Interval: 0s\n";
echo "On Event: /ppp secret set disabled=yes [/ppp secret find name=\"$test_username\"]\n";
echo "          /ppp active remove [find name=\"$test_username\"]\n";
echo "Comment: Auto disable PPP user $test_username on expiry date";
echo "</pre>";

echo "</div>";

echo "<h3>مقارنة النظام القديم والجديد:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f8f9fa;'>";
echo "<th>الخاصية</th>";
echo "<th style='color: #dc3545;'>النظام القديم</th>";
echo "<th style='color: #28a745;'>النظام الجديد</th>";
echo "</tr>";

$comparisons = [
    ['إدخال مدة الاشتراك', 'يدوي عند كل مشترك', 'تلقائي من البروفايل'],
    ['عرض مدة البروفايل', 'غير متوفر', 'يظهر في قائمة البروفايلات'],
    ['تاريخ بداية Scheduler', 'نفس تاريخ الانتهاء', 'تاريخ الإنشاء'],
    ['تاريخ انتهاء Scheduler', 'غير واضح', 'محسوب تلقائياً'],
    ['عرض الأيام المتبقية', 'لا يعمل', 'يعمل مع ألوان تحذيرية'],
    ['Scheduler مكرر', 'يحدث أحياناً', 'تم إصلاحه'],
    ['سهولة الاستخدام', 'معقد', 'بسيط وتلقائي']
];

foreach ($comparisons as $comparison) {
    echo "<tr>";
    echo "<td><strong>" . $comparison[0] . "</strong></td>";
    echo "<td style='color: #dc3545;'>" . $comparison[1] . "</td>";
    echo "<td style='color: #28a745;'>" . $comparison[2] . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>خطوات استخدام النظام الجديد:</h3>";
echo "<ol>";
echo "<li><strong>إنشاء بروفايل:</strong> أضف البروفايل مع تحديد مدة الباقة بالأيام</li>";
echo "<li><strong>إضافة مشترك:</strong> اختر البروفايل وستظهر المدة تلقائياً</li>";
echo "<li><strong>التأكيد:</strong> سيتم إنشاء scheduler التعطيل تلقائياً</li>";
echo "<li><strong>المتابعة:</strong> راقب الأيام المتبقية في قائمة المشتركين</li>";
echo "</ol>";

echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4 style='color: #155724; margin-top: 0;'>✅ تم تطبيق الإصلاحات التالية:</h4>";
echo "<ul style='color: #155724;'>";
echo "<li>إزالة حقل إدخال المدة من نموذج إضافة المشترك</li>";
echo "<li>أخذ المدة تلقائياً من البروفايل المحدد</li>";
echo "<li>عرض المدة في قائمة البروفايلات</li>";
echo "<li>إصلاح مشكلة تاريخ بداية الـ scheduler</li>";
echo "<li>تحسين عرض الأيام المتبقية مع الألوان</li>";
echo "</ul>";
echo "</div>";
?>
