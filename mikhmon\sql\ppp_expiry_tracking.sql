-- جدول لتتبع انتهاء اشتراكات PPP (اختياري)
-- PPP Subscription Expiry Tracking Table (Optional)

-- إن<PERSON>اء جدول لتتبع انتهاء الاشتراكات
CREATE TABLE IF NOT EXISTS ppp_expiry_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    profile_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    start_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    duration_days INT NOT NULL,
    status ENUM('active', 'expired', 'disabled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_status (status)
);

-- إنشاء جدول لسجل التنبيهات
CREATE TABLE IF NOT EXISTS ppp_expiry_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    alert_type ENUM('warning', 'expired') NOT NULL,
    days_remaining INT,
    alert_date DATE NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_alert_date (alert_date)
);

-- إنشاء view لعرض المشتركين القريبين من الانتهاء
CREATE OR REPLACE VIEW ppp_expiring_soon AS
SELECT 
    username,
    profile_name,
    expiry_date,
    DATEDIFF(expiry_date, CURDATE()) as days_remaining,
    CASE 
        WHEN DATEDIFF(expiry_date, CURDATE()) < 0 THEN 'expired'
        WHEN DATEDIFF(expiry_date, CURDATE()) <= 3 THEN 'warning'
        ELSE 'normal'
    END as alert_level
FROM ppp_expiry_tracking 
WHERE status = 'active'
ORDER BY days_remaining ASC;

-- إجراء مخزن لتحديث حالة المشتركين المنتهيين
DELIMITER //
CREATE PROCEDURE UpdateExpiredSubscriptions()
BEGIN
    -- تحديث حالة المشتركين المنتهيين
    UPDATE ppp_expiry_tracking 
    SET status = 'expired' 
    WHERE expiry_date < CURDATE() AND status = 'active';
    
    -- إدراج تنبيهات للمشتركين المنتهيين حديثاً
    INSERT INTO ppp_expiry_alerts (username, alert_type, days_remaining, alert_date)
    SELECT username, 'expired', DATEDIFF(expiry_date, CURDATE()), CURDATE()
    FROM ppp_expiry_tracking 
    WHERE expiry_date = DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
    AND status = 'expired';
    
    -- إدراج تنبيهات للمشتركين القريبين من الانتهاء
    INSERT INTO ppp_expiry_alerts (username, alert_type, days_remaining, alert_date)
    SELECT username, 'warning', DATEDIFF(expiry_date, CURDATE()), CURDATE()
    FROM ppp_expiry_tracking 
    WHERE DATEDIFF(expiry_date, CURDATE()) IN (1, 3, 7)
    AND status = 'active'
    AND username NOT IN (
        SELECT username FROM ppp_expiry_alerts 
        WHERE alert_date = CURDATE() AND alert_type = 'warning'
    );
END //
DELIMITER ;

-- إنشاء حدث لتشغيل الإجراء يومياً
CREATE EVENT IF NOT EXISTS daily_expiry_check
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 1 DAY
DO
CALL UpdateExpiredSubscriptions();

-- تفعيل Event Scheduler
SET GLOBAL event_scheduler = ON;

-- استعلامات مفيدة للتقارير

-- عرض المشتركين المنتهيين اليوم
-- SELECT * FROM ppp_expiry_tracking WHERE expiry_date = CURDATE();

-- عرض المشتركين القريبين من الانتهاء (خلال 7 أيام)
-- SELECT * FROM ppp_expiring_soon WHERE days_remaining BETWEEN 0 AND 7;

-- عرض إحصائيات الانتهاء
-- SELECT 
--     alert_level,
--     COUNT(*) as count
-- FROM ppp_expiring_soon 
-- GROUP BY alert_level;

-- عرض المشتركين حسب البروفايل
-- SELECT 
--     profile_name,
--     COUNT(*) as total_users,
--     SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
--     SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_users
-- FROM ppp_expiry_tracking 
-- GROUP BY profile_name;
