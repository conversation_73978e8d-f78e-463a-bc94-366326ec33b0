<?php
/*
 * شرح الفرق بين أنواع الـ Schedulers في نظام PPP
 */

echo "<h1>🔍 شرح أنواع الـ Schedulers في نظام PPP</h1>";

echo "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 الـ Scheduler في الصورة التي أرسلتها:</h2>";
echo "<ul>";
echo "<li><strong>الاسم:</strong> monitor-99</li>";
echo "<li><strong>النوع:</strong> scheduler مراقبة</li>";
echo "<li><strong>تاريخ البداية:</strong> Jul/04/2025 (تاريخ إنشاء المستخدم)</li>";
echo "<li><strong>التشغيل التالي:</strong> Jul/04/2025 20:20:41</li>";
echo "<li><strong>الفترة:</strong> 30d 00:00:00 (كل 30 يوم)</li>";
echo "</ul>";
echo "<p style='color: #28a745; font-weight: bold;'>✅ هذا الـ scheduler طبيعي تماماً!</p>";
echo "</div>";

echo "<h2>🔄 أنواع الـ Schedulers في النظام:</h2>";

echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";

// Scheduler المراقبة
echo "<div style='flex: 1; background-color: #e3f2fd; padding: 15px; border-radius: 8px; border-left: 4px solid #2196f3;'>";
echo "<h3 style='color: #1976d2; margin-top: 0;'>🔍 Scheduler المراقبة</h3>";
echo "<p><strong>الاسم:</strong> monitor-username</p>";
echo "<p><strong>الغرض:</strong> مراقبة دورية للمستخدم</p>";
echo "<p><strong>تاريخ البداية:</strong> تاريخ إنشاء المستخدم</p>";
echo "<p><strong>التكرار:</strong> حسب الفترة المحددة (مثل كل 30 يوم)</p>";
echo "<p><strong>متى ينشأ:</strong> فقط إذا حددت interval يدوياً</p>";
echo "<p style='color: #1976d2;'><strong>مثال:</strong> monitor-99 يعمل كل 30 يوم للمراقبة</p>";
echo "</div>";

// Scheduler التعطيل
echo "<div style='flex: 1; background-color: #ffebee; padding: 15px; border-radius: 8px; border-left: 4px solid #f44336;'>";
echo "<h3 style='color: #d32f2f; margin-top: 0;'>🚫 Scheduler التعطيل التلقائي</h3>";
echo "<p><strong>الاسم:</strong> disable-ppp-username</p>";
echo "<p><strong>الغرض:</strong> تعطيل المستخدم عند انتهاء الاشتراك</p>";
echo "<p><strong>تاريخ البداية:</strong> تاريخ انتهاء الاشتراك</p>";
echo "<p><strong>التكرار:</strong> مرة واحدة فقط (0s)</p>";
echo "<p><strong>متى ينشأ:</strong> تلقائياً عند إنشاء أي مستخدم</p>";
echo "<p style='color: #d32f2f;'><strong>مثال:</strong> disable-ppp-99 يعمل مرة واحدة بعد 30 يوم</p>";
echo "</div>";

echo "</div>";

echo "<h2>📊 مثال عملي - مستخدم اشتراك 30 يوم:</h2>";

$today = date('M/d/Y');
$expiry_date = date('M/d/Y', strtotime('+30 days'));

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
echo "<tr style='background-color: #f8f9fa;'>";
echo "<th>نوع الـ Scheduler</th>";
echo "<th>الاسم</th>";
echo "<th>تاريخ البداية</th>";
echo "<th>الفترة</th>";
echo "<th>الغرض</th>";
echo "<th>الحالة</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='color: #2196f3; font-weight: bold;'>مراقبة</td>";
echo "<td>monitor-99</td>";
echo "<td>$today</td>";
echo "<td>30d 00:00:00</td>";
echo "<td>مراقبة دورية كل 30 يوم</td>";
echo "<td style='color: #28a745;'>✅ طبيعي</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='color: #f44336; font-weight: bold;'>تعطيل تلقائي</td>";
echo "<td>disable-ppp-99</td>";
echo "<td>$expiry_date</td>";
echo "<td>0s</td>";
echo "<td>تعطيل المستخدم عند انتهاء الاشتراك</td>";
echo "<td style='color: #ff9800;'>⏳ ينتظر التاريخ</td>";
echo "</tr>";

echo "</table>";

echo "<h2>❓ لماذا يظهر نفس التاريخ في scheduler المراقبة؟</h2>";

echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4 style='color: #856404; margin-top: 0;'>الإجابة:</h4>";
echo "<p style='color: #856404;'>scheduler المراقبة (monitor-99) يبدأ من <strong>تاريخ إنشاء المستخدم</strong> ويتكرر كل 30 يوم للمراقبة.</p>";
echo "<p style='color: #856404;'>هذا <strong>طبيعي تماماً</strong> لأن:</p>";
echo "<ul style='color: #856404;'>";
echo "<li>Start Date = تاريخ الإنشاء (اليوم)</li>";
echo "<li>Next Run = أول تشغيل (اليوم + الوقت المحدد)</li>";
echo "<li>بعد ذلك سيتكرر كل 30 يوم</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔧 كيفية التحقق من scheduler التعطيل:</h2>";

echo "<div style='background-color: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4 style='color: #2e7d32; margin-top: 0;'>خطوات التحقق:</h4>";
echo "<ol style='color: #2e7d32;'>";
echo "<li>اذهب إلى <strong>System → Scheduler</strong> في MikroTik</li>";
echo "<li>ابحث عن scheduler باسم <strong>disable-ppp-99</strong></li>";
echo "<li>تحقق من أن تاريخ البداية = تاريخ اليوم + 30 يوم</li>";
echo "<li>تحقق من أن الفترة = <strong>0s</strong> (مرة واحدة فقط)</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🛠️ إذا لم تجد scheduler التعطيل:</h2>";

echo "<div style='background-color: #ffebee; border: 1px solid #f44336; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4 style='color: #c62828; margin-top: 0;'>الأسباب المحتملة:</h4>";
echo "<ul style='color: #c62828;'>";
echo "<li>البروفايل لا يحتوي على مدة محددة (duration_days)</li>";
echo "<li>خطأ في إنشاء المستخدم</li>";
echo "<li>تم حذف الـ scheduler يدوياً</li>";
echo "</ul>";
echo "<h4 style='color: #c62828;'>الحل:</h4>";
echo "<ol style='color: #c62828;'>";
echo "<li>تأكد من أن البروفايل يحتوي على مدة محددة</li>";
echo "<li>أعد إنشاء المستخدم</li>";
echo "<li>استخدم ملف <strong>check_schedulers.php</strong> للفحص</li>";
echo "</ol>";
echo "</div>";

echo "<h2>📝 ملخص:</h2>";

echo "<div style='background-color: #f0f8ff; border: 2px solid #4169e1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4 style='color: #4169e1; margin-top: 0;'>النتيجة:</h4>";
echo "<p style='color: #4169e1; font-size: 16px;'><strong>الـ scheduler في الصورة (monitor-99) طبيعي 100%</strong></p>";
echo "<p style='color: #4169e1;'>هو scheduler مراقبة يبدأ من تاريخ الإنشاء ويتكرر كل 30 يوم.</p>";
echo "<p style='color: #4169e1;'>scheduler التعطيل التلقائي (disable-ppp-99) منفصل ويبدأ بعد 30 يوم.</p>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='check_schedulers.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🔍 فحص جميع الـ Schedulers</a>";
echo "</div>";
?>
