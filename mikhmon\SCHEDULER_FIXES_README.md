# إصلاحات نظام الجدولة في MikhMon - Scheduler System Fixes

## المشاكل التي تم إصلاحها

### 1. مشكلة تاريخ بداية الجدولة يظهر بنفس تاريخ الانتهاء
**المشكلة:** كان يتم إنشاء scheduler إضافي غير ضروري بنفس اسم المستخدم، مما يسبب تضارب.

**الحل:** 
- فصل scheduler المراقبة عن scheduler التعطيل
- إعطاء أسماء مختلفة: `disable-ppp-username` للتعطيل و `monitor-username` للمراقبة
- إنشاء scheduler المراقبة فقط عند الحاجة (عند تحديد interval)

### 2. عدم عرض تاريخ الانتهاء في قائمة المشتركين
**المشكلة:** كان البحث عن scheduler يتم بطريقة خاطئة أحياناً.

**الحل:**
- تحسين البحث عن scheduler بالاسم الصحيح `disable-ppp-username`
- إضافة fallback للبحث في تعليق المستخدم إذا لم يوجد scheduler
- تحسين عرض التاريخ بتنسيق صحيح

### 3. عدم عرض عدد الأيام المتبقية
**المشكلة:** حساب الأيام المتبقية كان غير دقيق ولا يعرض بشكل صحيح.

**الحل:**
- إصلاح حساب الفرق بين التواريخ
- تعيين نهاية اليوم (23:59:59) لحساب أكثر دقة
- إضافة ألوان تحذيرية:
  - أحمر: للاشتراكات المنتهية
  - أصفر: للاشتراكات القريبة من الانتهاء (3 أيام أو أقل)
  - أخضر: للاشتراكات العادية

## الملفات المُعدلة

### 1. `mikhmon/ppp/addsecret.php`
- إصلاح إنشاء scheduler مكرر
- فصل scheduler التعطيل عن المراقبة
- تحسين أسماء scheduler

### 2. `mikhmon/ppp/pppsecrets.php`
- إصلاح عرض تاريخ الانتهاء
- تحسين حساب الأيام المتبقية
- إضافة ألوان تحذيرية
- تحسين البحث في scheduler والتعليقات

### 3. `mikhmon/ppp/pppactive.php`
- إضافة نفس التحسينات لقائمة الاتصالات النشطة
- توحيد طريقة عرض البيانات

### 4. `mikhmon/ppp/addpppprofile.php`
- إضافة حقل مدة الباقة بالأيام
- حفظ المدة في تعليق البروفايل

## كيفية عمل النظام الجديد

### 1. إنشاء بروفايل جديد
- يتم إضافة حقل "مدة الباقة (عدد الأيام)"
- يتم حفظ المدة في تعليق البروفايل بصيغة `duration_days=30`

### 2. إضافة مشترك جديد
- يتم استخراج مدة الباقة من بروفايل المستخدم
- يتم حساب تاريخ الانتهاء تلقائياً
- يتم إنشاء scheduler للتعطيل التلقائي بتاريخ الانتهاء
- يتم حفظ تاريخ الانتهاء في تعليق المستخدم

### 3. عرض المشتركين
- يتم البحث عن تاريخ الانتهاء في scheduler أولاً
- إذا لم يوجد، يتم البحث في تعليق المستخدم
- يتم حساب الأيام المتبقية وعرضها بألوان مناسبة

## اختبار الإصلاحات

يمكن تشغيل ملف الاختبار `test_scheduler_fix.php` للتأكد من عمل الإصلاحات بشكل صحيح.

## ملاحظات مهمة

1. **النسخ الاحتياطي:** تأكد من أخذ نسخة احتياطية قبل تطبيق الإصلاحات
2. **قاعدة البيانات:** الإصلاحات تعمل مع MikroTik API ولا تحتاج تعديل قاعدة بيانات
3. **التوافق:** الإصلاحات متوافقة مع الإصدارات الحالية من MikhMon
4. **الأداء:** تم تحسين الاستعلامات لتقليل الحمل على MikroTik

## الدعم

في حالة وجود مشاكل، تأكد من:
- صحة اتصال MikroTik API
- وجود صلاحيات كافية للمستخدم
- تفعيل System Scheduler في MikroTik
